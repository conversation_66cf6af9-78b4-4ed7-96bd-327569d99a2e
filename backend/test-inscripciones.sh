#!/bin/bash

# Script para ejecutar tests de inscripciones
# Uso: ./test-inscripciones.sh [test-name]

echo "🧪 Ejecutando Tests de Inscripciones a Ligas"
echo "=========================================="

# Función para ejecutar un test específico
run_test() {
    local test_name=$1
    echo "📋 Ejecutando: $test_name"
    echo "----------------------------------------"
    npm test -- --testPathPattern="$test_name" --verbose
    echo ""
}

# Si se proporciona un argumento, ejecutar solo ese test
if [ $# -eq 1 ]; then
    case $1 in
        "simple")
            run_test "inscripcion-simple.test.ts"
            ;;
        "flujo")
            run_test "inscripcion-flujo.test.ts"
            ;;
        "early-bird")
            run_test "inscripcion-early-bird.test.ts"
            ;;
        *)
            echo "❌ Test no reconocido: $1"
            echo "Tests disponibles: simple, flujo, early-bird"
            exit 1
            ;;
    esac
else
    # Ejecutar todos los tests funcionales
    echo "🚀 Ejecutando todos los tests funcionales de inscripciones..."
    echo ""
    
    run_test "inscripcion-simple.test.ts"
    run_test "inscripcion-flujo.test.ts"
    run_test "inscripcion-early-bird.test.ts"
    
    echo "✅ Tests de inscripciones completados"
fi
