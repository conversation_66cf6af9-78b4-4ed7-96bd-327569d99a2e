import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InscripcionesService } from '../../modules/inscripciones/inscripciones.service';
import { LigasService } from '../../modules/ligas/ligas.service';
import { UsuariosService } from '../../modules/usuarios/usuarios.service';
import { Inscripcion } from '../../modules/inscripciones/entities/inscripcion.entity';
import { Liga } from '../../modules/ligas/entities/liga.entity';
import { Usuario } from '../../modules/usuarios/entities/usuario.entity';
import { Box } from '../../modules/boxes/entities/box.entity';
import { NivelUsuario, GeneroUsuario } from '../../modules/usuarios/entities/usuario.entity';

/**
 * Test simple de inscripción para verificar configuración básica
 */
describe('Inscripción Simple Test', () => {
  let inscripcionesService: InscripcionesService;
  let ligasService: LigasService;
  let usuariosService: UsuariosService;
  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'postgres',
          host: process.env.DB_HOST || 'localhost',
          port: parseInt(process.env.DB_PORT || '5432', 10),
          username: process.env.DB_USERNAME || 'postgres',
          password: process.env.DB_PASSWORD || 'postgres',
          database: process.env.DB_DATABASE || 'wodleague',
          entities: [Inscripcion, Liga, Usuario, Box],
          synchronize: false, // No sincronizar en tests
          logging: false,
        }),
        TypeOrmModule.forFeature([Inscripcion, Liga, Usuario, Box]),
      ],
      providers: [InscripcionesService, LigasService, UsuariosService],
    }).compile();

    inscripcionesService = module.get<InscripcionesService>(InscripcionesService);
    ligasService = module.get<LigasService>(LigasService);
    usuariosService = module.get<UsuariosService>(UsuariosService);
  });

  afterAll(async () => {
    await module.close();
  });

  it('debe estar definido', () => {
    expect(inscripcionesService).toBeDefined();
    expect(ligasService).toBeDefined();
    expect(usuariosService).toBeDefined();
  });

  it('debe poder crear un usuario de prueba', async () => {
    const userData = {
      nombre: 'Usuario Test Simple',
      alias: 'usuario_test_simple',
      email: '<EMAIL>',
      password: 'TestPassword123!',
      nivel: NivelUsuario.RX,
      genero: GeneroUsuario.MASCULINO,
    };

    try {
      const user = await usuariosService.create(userData);
      expect(user).toBeDefined();
      expect(user.id).toBeDefined();
      expect(user.email).toBe(userData.email);
      
      // Limpiar después del test
      await usuariosService.remove(user.id);
    } catch (error) {
      console.log('Error al crear usuario:', error.message);
      // Si el usuario ya existe, intentar encontrarlo y eliminarlo
      try {
        const existingUser = await usuariosService.findByEmail(userData.email);
        if (existingUser) {
          await usuariosService.remove(existingUser.id);
        }
      } catch (cleanupError) {
        console.log('Error en limpieza:', cleanupError.message);
      }
    }
  });

  it('debe poder crear una liga de prueba', async () => {
    const ligaData = {
      nombre: 'Liga Test Simple',
      descripcion: 'Liga de prueba simple',
      fechaInicio: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // En 30 días
      fechaFin: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // En 90 días
      duracionSemanas: 8,
      precioInscripcion: 100,
      tieneDescuentoEarly: false,
      categoriaRx: true,
      categoriaIntermedio: true,
      categoriaScaled: true,
      generoMasculino: true,
      generoFemenino: true,
    };

    try {
      const liga = await ligasService.create(ligaData);
      expect(liga).toBeDefined();
      expect(liga.id).toBeDefined();
      expect(liga.nombre).toBe(ligaData.nombre);
      
      // Limpiar después del test
      await ligasService.remove(liga.id);
    } catch (error) {
      console.log('Error al crear liga:', error.message);
      // Si la liga ya existe, intentar encontrarla y eliminarla
      try {
        const ligas = await ligasService.findAll();
        const existingLiga = ligas.find(l => l.nombre === ligaData.nombre);
        if (existingLiga) {
          await ligasService.remove(existingLiga.id);
        }
      } catch (cleanupError) {
        console.log('Error en limpieza:', cleanupError.message);
      }
    }
  });
});
