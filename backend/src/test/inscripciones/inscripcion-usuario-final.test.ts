import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InscripcionesService } from '../../modules/inscripciones/inscripciones.service';
import { LigasService } from '../../modules/ligas/ligas.service';
import { UsuariosService } from '../../modules/usuarios/usuarios.service';
import { Inscripcion, EstadoInscripcion, MetodoPago } from '../../modules/inscripciones/entities/inscripcion.entity';
import { Liga, EstadoLiga } from '../../modules/ligas/entities/liga.entity';
import { Usuario, NivelUsuario, GeneroUsuario } from '../../modules/usuarios/entities/usuario.entity';
import { Box } from '../../modules/boxes/entities/box.entity';

/**
 * Test del flujo completo desde la perspectiva del usuario final
 * 
 * Este test simula el comportamiento real de un usuario que:
 * 1. Ve las ligas disponibles
 * 2. Se inscribe a una liga
 * 3. Realiza el pago
 * 4. Verifica su inscripción
 */
describe('Flujo de Usuario Final - Inscripción a Liga', () => {
  let inscripcionesService: InscripcionesService;
  let ligasService: LigasService;
  let usuariosService: UsuariosService;
  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'postgres',
          host: process.env.DB_HOST || 'localhost',
          port: parseInt(process.env.DB_PORT || '5432', 10),
          username: process.env.DB_USERNAME || 'postgres',
          password: process.env.DB_PASSWORD || 'postgres',
          database: process.env.DB_DATABASE || 'wodleague',
          entities: [Inscripcion, Liga, Usuario, Box],
          synchronize: false,
          logging: false,
        }),
        TypeOrmModule.forFeature([Inscripcion, Liga, Usuario, Box]),
      ],
      providers: [InscripcionesService, LigasService, UsuariosService],
    }).compile();

    inscripcionesService = module.get<InscripcionesService>(InscripcionesService);
    ligasService = module.get<LigasService>(LigasService);
    usuariosService = module.get<UsuariosService>(UsuariosService);
  });

  afterAll(async () => {
    await module.close();
  });

  it('debe completar el flujo completo de inscripción de un usuario', async () => {
    console.log('\n🎯 INICIANDO FLUJO COMPLETO DE INSCRIPCIÓN');
    console.log('==========================================');

    // PASO 1: Crear usuario (simulando registro)
    console.log('\n👤 PASO 1: Creando usuario...');
    const userData = {
      nombre: 'Juan Pérez',
      alias: 'juan_crossfit',
      email: `juan.perez.${Date.now()}@example.com`,
      password: 'MiPassword123!',
      nivel: NivelUsuario.RX,
      genero: GeneroUsuario.MASCULINO,
    };

    const usuario = await usuariosService.create(userData);
    console.log(`✅ Usuario creado: ${usuario.nombre} (${usuario.email})`);
    console.log(`   - ID: ${usuario.id}`);
    console.log(`   - Nivel: ${usuario.nivel}`);
    console.log(`   - Género: ${usuario.genero}`);

    // PASO 2: Crear liga disponible (simulando liga creada por admin)
    console.log('\n🏆 PASO 2: Creando liga disponible...');
    const ligaData = {
      nombre: `Liga CrossFit Primavera ${new Date().getFullYear()}`,
      descripcion: 'Liga de primavera para todos los niveles. ¡Únete y demuestra tu fuerza!',
      fechaInicio: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000), // En 45 días
      fechaFin: new Date(Date.now() + 101 * 24 * 60 * 60 * 1000), // 8 semanas después
      duracionSemanas: 8,
      estado: EstadoLiga.PREPARACION,
      precioInscripcion: 150,
      tieneDescuentoEarly: true,
      precioEarly: 120,
      categoriaRx: true,
      categoriaIntermedio: true,
      categoriaScaled: true,
      generoMasculino: true,
      generoFemenino: true,
    };

    const liga = await ligasService.create(ligaData);
    console.log(`✅ Liga creada: ${liga.nombre}`);
    console.log(`   - ID: ${liga.id}`);
    console.log(`   - Fecha inicio: ${liga.fechaInicio.toLocaleDateString()}`);
    console.log(`   - Precio: $${liga.precioInscripcion}`);
    console.log(`   - Precio Early Bird: $${liga.precioEarly}`);
    console.log(`   - Estado: ${liga.estado}`);

    // PASO 3: Usuario ve las ligas disponibles
    console.log('\n📋 PASO 3: Usuario consulta ligas disponibles...');
    const ligasDisponibles = await ligasService.findAll();
    const ligasActivas = ligasDisponibles.filter(l => 
      l.estado === EstadoLiga.PREPARACION || l.estado === EstadoLiga.ACTIVA
    );
    console.log(`✅ Encontradas ${ligasActivas.length} ligas disponibles para inscripción`);

    // PASO 4: Usuario se inscribe a la liga
    console.log('\n📝 PASO 4: Usuario se inscribe a la liga...');
    const categoria = `${usuario.nivel}-${usuario.genero}`;
    const inscripcionData = {
      usuarioId: usuario.id,
      ligaId: liga.id,
      fechaInscripcion: new Date(),
      categoria: categoria,
    };

    const inscripcion = await inscripcionesService.create(inscripcionData);
    console.log(`✅ Inscripción creada exitosamente`);
    console.log(`   - ID: ${inscripcion.id}`);
    console.log(`   - Estado: ${inscripcion.estado}`);
    console.log(`   - Categoría: ${inscripcion.categoria}`);
    console.log(`   - Early Bird: ${inscripcion.esEarly ? 'SÍ' : 'NO'}`);
    console.log(`   - Monto a pagar: $${inscripcion.esEarly ? liga.precioEarly : liga.precioInscripcion}`);

    // Verificar que se aplicó el descuento early bird
    expect(inscripcion.esEarly).toBe(true);
    expect(inscripcion.estado).toBe(EstadoInscripcion.PENDIENTE);
    expect(Number(inscripcion.montoPagado)).toBe(0);

    // PASO 5: Usuario consulta sus inscripciones
    console.log('\n📊 PASO 5: Usuario consulta sus inscripciones...');
    const inscripcionesUsuario = await inscripcionesService.findByUsuario(usuario.id);
    console.log(`✅ Usuario tiene ${inscripcionesUsuario.length} inscripción(es)`);
    
    const inscripcionActual = inscripcionesUsuario.find(i => i.id === inscripcion.id);
    expect(inscripcionActual).toBeDefined();

    // PASO 6: Usuario realiza el pago (simulando confirmación de admin)
    console.log('\n💳 PASO 6: Procesando pago...');
    const montoPagar = inscripcion.esEarly ? liga.precioEarly : liga.precioInscripcion;
    const pagoData = {
      fechaPago: new Date(),
      metodoPago: MetodoPago.TRANSFERENCIA,
      referenciaPago: `TRF-${Date.now()}`,
      montoPagado: montoPagar,
      comentarios: 'Pago realizado por transferencia bancaria',
    };

    const inscripcionPagada = await inscripcionesService.confirmarPago(inscripcion.id, pagoData);
    console.log(`✅ Pago confirmado exitosamente`);
    console.log(`   - Estado: ${inscripcionPagada.estado}`);
    console.log(`   - Método: ${inscripcionPagada.metodoPago}`);
    console.log(`   - Referencia: ${inscripcionPagada.referenciaPago}`);
    console.log(`   - Monto: $${inscripcionPagada.montoPagado}`);

    expect(inscripcionPagada.estado).toBe(EstadoInscripcion.PAGADA);
    expect(Number(inscripcionPagada.montoPagado)).toBe(montoPagar);

    // PASO 7: Verificar estadísticas de la liga
    console.log('\n📈 PASO 7: Verificando estadísticas de la liga...');
    const stats = await inscripcionesService.contarInscripcionesPorLiga(liga.id);
    console.log(`✅ Estadísticas de la liga:`);
    console.log(`   - Total inscripciones: ${stats.total}`);
    console.log(`   - Pagadas: ${stats.pagadas}`);
    console.log(`   - Pendientes: ${stats.pendientes}`);
    console.log(`   - Canceladas: ${stats.canceladas}`);

    expect(stats.total).toBeGreaterThan(0);
    expect(stats.pagadas).toBeGreaterThan(0);

    // PASO 8: Usuario consulta inscripciones de la liga (ver competidores)
    console.log('\n👥 PASO 8: Consultando participantes de la liga...');
    const participantes = await inscripcionesService.findByLiga(liga.id);
    console.log(`✅ La liga tiene ${participantes.length} participante(s) inscrito(s)`);
    
    const participanteActual = participantes.find(p => p.usuarioId === usuario.id);
    expect(participanteActual).toBeDefined();
    expect(participanteActual?.estado).toBe(EstadoInscripcion.PAGADA);

    console.log('\n🎉 FLUJO COMPLETADO EXITOSAMENTE');
    console.log('================================');
    console.log('✅ Usuario registrado');
    console.log('✅ Liga creada');
    console.log('✅ Inscripción realizada');
    console.log('✅ Descuento early bird aplicado');
    console.log('✅ Pago confirmado');
    console.log('✅ Estadísticas actualizadas');
    console.log('✅ Participación verificada');

    // LIMPIEZA: Eliminar datos de prueba
    console.log('\n🧹 Limpiando datos de prueba...');
    await inscripcionesService.remove(inscripcion.id);
    await usuariosService.remove(usuario.id);
    await ligasService.remove(liga.id);
    console.log('✅ Limpieza completada');
  });

  it('debe manejar el caso de usuario que se inscribe tarde (sin early bird)', async () => {
    console.log('\n⏰ CASO: Inscripción tardía (sin early bird)');
    console.log('============================================');

    // Crear usuario
    const userData = {
      nombre: 'María González',
      alias: 'maria_fit',
      email: `maria.gonzalez.${Date.now()}@example.com`,
      password: 'MiPassword123!',
      nivel: NivelUsuario.INTERMEDIO,
      genero: GeneroUsuario.FEMENINO,
    };

    const usuario = await usuariosService.create(userData);
    console.log(`👤 Usuario creado: ${usuario.nombre}`);

    // Crear liga que comienza pronto (sin early bird)
    const ligaData = {
      nombre: `Liga Verano ${new Date().getFullYear()}`,
      descripcion: 'Liga de verano - inscripciones tardías',
      fechaInicio: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000), // En 20 días
      fechaFin: new Date(Date.now() + 76 * 24 * 60 * 60 * 1000), // 8 semanas después
      duracionSemanas: 8,
      estado: EstadoLiga.PREPARACION,
      precioInscripcion: 150,
      tieneDescuentoEarly: true,
      precioEarly: 120,
      categoriaRx: true,
      categoriaIntermedio: true,
      categoriaScaled: true,
      generoMasculino: true,
      generoFemenino: true,
    };

    const liga = await ligasService.create(ligaData);
    console.log(`🏆 Liga creada: ${liga.nombre} (comienza en 20 días)`);

    // Inscribirse (no debería tener early bird)
    const inscripcionData = {
      usuarioId: usuario.id,
      ligaId: liga.id,
      fechaInscripcion: new Date(),
      categoria: `${usuario.nivel}-${usuario.genero}`,
    };

    const inscripcion = await inscripcionesService.create(inscripcionData);
    console.log(`📝 Inscripción creada - Early Bird: ${inscripcion.esEarly ? 'SÍ' : 'NO'}`);
    console.log(`💰 Precio a pagar: $${liga.precioInscripcion} (precio regular)`);

    // Verificar que NO se aplicó early bird
    expect(inscripcion.esEarly).toBe(false);

    // Limpieza
    await inscripcionesService.remove(inscripcion.id);
    await usuariosService.remove(usuario.id);
    await ligasService.remove(liga.id);
    console.log('✅ Caso completado y limpiado');
  });
});
