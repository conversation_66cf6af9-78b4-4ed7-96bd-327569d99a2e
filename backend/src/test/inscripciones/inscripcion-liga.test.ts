import { Logger } from '@nestjs/common';
import { 
  DatabaseTestHelper,
  UsuariosTestHelper,
  LigasTestHelper,
  InscripcionesTestHelper,
  generateTestUser,
  generateCreateLigaDto
} from '../helper';
import { EstadoInscripcion } from '../../modules/inscripciones/entities/inscripcion.entity';
import { EstadoLiga } from '../../modules/ligas/entities/liga.entity';
import { NivelUsuario, GeneroUsuario } from '../../modules/usuarios/entities/usuario.entity';

/**
 * Test de Inscripción a Liga
 * 
 * Este test verifica el flujo completo de inscripción de un usuario a una liga:
 * 1. Creación de usuario y liga
 * 2. Inscripción del usuario a la liga
 * 3. Verificación de datos de inscripción
 * 4. Confirmación de pago
 * 5. Validaciones de negocio
 */
describe('Inscripción a Liga Tests', () => {
  let inscripcionesService: any;
  let ligasService: any;
  let usuariosService: any;
  let testUserId: string | null = null;
  let testLigaId: string | null = null;
  let testInscripcionId: string | null = null;
  const logger = new Logger('InscripcionLigaTest');

  beforeAll(async () => {
    // Verificar conexión a la base de datos antes de iniciar las pruebas
    if (!(await DatabaseTestHelper.checkDatabaseConnection())) {
      throw new Error('Error de conexión a la base de datos');
    }

    // Inicializar helpers y servicios
    usuariosService = await UsuariosTestHelper.initialize();
    ligasService = await LigasTestHelper.initialize();
    const services = await InscripcionesTestHelper.initialize();
    inscripcionesService = services.inscripcionesService;

    logger.log('Servicios inicializados correctamente');
  });

  beforeEach(async () => {
    // Crear usuario de prueba para cada test
    const testUserData = generateTestUser('inscripcion');
    const createdUser = await UsuariosTestHelper.createTestUser(testUserData);
    testUserId = createdUser.id;

    // Crear liga de prueba para cada test
    const testLiga = await LigasTestHelper.createTestLiga('inscripcion');
    testLigaId = testLiga.id;

    logger.log(`Usuario de prueba creado: ${testUserId}`);
    logger.log(`Liga de prueba creada: ${testLigaId}`);
  });

  afterEach(async () => {
    // Limpiar datos de prueba después de cada test
    try {
      if (testInscripcionId) {
        await inscripcionesService.remove(testInscripcionId);
        testInscripcionId = null;
      }
      if (testUserId) {
        await UsuariosTestHelper.cleanup();
        testUserId = null;
      }
      if (testLigaId) {
        await LigasTestHelper.cleanup();
        testLigaId = null;
      }
    } catch (error) {
      logger.warn('Error durante la limpieza:', error.message);
    }
  });

  afterAll(async () => {
    // Limpieza final
    await InscripcionesTestHelper.cleanup();
    await LigasTestHelper.cleanup();
    await UsuariosTestHelper.cleanup();
  });

  // Test básico de inscripción exitosa
  it('debe permitir que un usuario se inscriba a una liga exitosamente', async () => {
    // Arrange: Preparar datos de inscripción
    const createInscripcionDto = {
      usuarioId: testUserId!,
      ligaId: testLigaId!,
      fechaInscripcion: new Date(),
      categoria: 'RX-Masculino',
    };

    // Act: Crear la inscripción
    const result = await inscripcionesService.create(createInscripcionDto);
    testInscripcionId = result.id; // Guardar para limpieza

    // Assert: Verificar que la inscripción se creó correctamente
    expect(result).toBeDefined();
    expect(result.id).toBeDefined();
    expect(result.usuarioId).toBe(testUserId!);
    expect(result.ligaId).toBe(testLigaId!);
    expect(result.estado).toBe(EstadoInscripcion.PENDIENTE);
    expect(result.categoria).toBe('RX-Masculino');
    expect(result.fechaInscripcion).toBeDefined();
    expect(result.montoPagado).toBe(0);
    expect(result.esEarly).toBeDefined();

    logger.log(`Inscripción creada exitosamente: ${result.id}`);
  });

  // Test de inscripción con descuento early bird
  it('debe aplicar descuento early bird cuando corresponde', async () => {
    // Arrange: Crear liga con descuento early bird
    const ligaEarly = await LigasTestHelper.createLigaConDescuentoEarly('early');
    
    const createInscripcionDto = {
      usuarioId: testUserId!,
      ligaId: ligaEarly.id,
      fechaInscripcion: new Date(),
      categoria: 'RX-Masculino',
    };

    // Act: Crear la inscripción
    const result = await inscripcionesService.create(createInscripcionDto);
    testInscripcionId = result.id;

    // Assert: Verificar que se aplicó el descuento early bird
    expect(result.esEarly).toBe(true);
    
    logger.log(`Inscripción early bird creada: ${result.id}, esEarly: ${result.esEarly}`);
  });

  // Test de validación: no permitir inscripción duplicada
  it('debe rechazar inscripción duplicada del mismo usuario en la misma liga', async () => {
    // Arrange: Crear primera inscripción
    const createInscripcionDto = {
      usuarioId: testUserId!,
      ligaId: testLigaId!,
      fechaInscripcion: new Date(),
      categoria: 'RX-Masculino',
    };

    const firstInscripcion = await inscripcionesService.create(createInscripcionDto);
    testInscripcionId = firstInscripcion.id;

    // Act & Assert: Intentar crear segunda inscripción debe fallar
    await expect(
      inscripcionesService.create(createInscripcionDto)
    ).rejects.toThrow();

    logger.log('Inscripción duplicada rechazada correctamente');
  });

  // Test de validación: no permitir inscripción en liga finalizada
  it('debe rechazar inscripción en liga finalizada', async () => {
    // Arrange: Crear liga finalizada
    const ligaFinalizada = await LigasTestHelper.createLigaFinalizada('finalizada');
    
    const createInscripcionDto = {
      usuarioId: testUserId!,
      ligaId: ligaFinalizada.id,
      fechaInscripcion: new Date(),
      categoria: 'RX-Masculino',
    };

    // Act & Assert: Intentar inscribirse en liga finalizada debe fallar
    await expect(
      inscripcionesService.create(createInscripcionDto)
    ).rejects.toThrow();

    logger.log('Inscripción en liga finalizada rechazada correctamente');
  });

  // Test de confirmación de pago
  it('debe confirmar el pago de una inscripción correctamente', async () => {
    // Arrange: Crear inscripción pendiente
    const inscripcion = await InscripcionesTestHelper.createTestInscripcion(
      testUserId!,
      testLigaId!,
      'RX-Masculino'
    );
    testInscripcionId = inscripcion.id;

    const confirmarPagoDto = {
      fechaPago: new Date(),
      metodoPago: 'Transferencia' as any,
      referenciaPago: 'REF-TEST-123',
      montoPagado: 100,
      comentarios: 'Pago confirmado en test',
    };

    // Act: Confirmar el pago
    const result = await inscripcionesService.confirmarPago(inscripcion.id, confirmarPagoDto);

    // Assert: Verificar que el pago se confirmó
    expect(result.estado).toBe(EstadoInscripcion.PAGADA);
    expect(result.fechaPago).toBeDefined();
    expect(result.metodoPago).toBe('Transferencia');
    expect(result.referenciaPago).toBe('REF-TEST-123');
    expect(result.montoPagado).toBe(100);

    logger.log(`Pago confirmado para inscripción: ${result.id}`);
  });

  // Test de búsqueda de inscripciones por usuario
  it('debe encontrar inscripciones por usuario', async () => {
    // Arrange: Crear múltiples inscripciones para el usuario
    const liga2 = await LigasTestHelper.createTestLiga('segunda');

    const inscripcion1 = await InscripcionesTestHelper.createTestInscripcion(
      testUserId!,
      testLigaId!,
      'RX-Masculino'
    );

    const inscripcion2 = await InscripcionesTestHelper.createTestInscripcion(
      testUserId!,
      liga2.id,
      'RX-Masculino'
    );

    // Act: Buscar inscripciones del usuario
    const result = await inscripcionesService.findByUsuario(testUserId!);

    // Assert: Verificar que se encontraron las inscripciones
    expect(result).toBeDefined();
    expect(result.length).toBeGreaterThanOrEqual(2);

    const foundIds = result.map(i => i.id);
    expect(foundIds).toContain(inscripcion1.id);
    expect(foundIds).toContain(inscripcion2.id);

    logger.log(`Encontradas ${result.length} inscripciones para el usuario`);
  });

  // Test de cancelación de inscripción
  it('debe cancelar una inscripción pendiente correctamente', async () => {
    // Arrange: Crear inscripción pendiente
    const inscripcion = await InscripcionesTestHelper.createTestInscripcion(
      testUserId!,
      testLigaId!,
      'RX-Masculino'
    );
    testInscripcionId = inscripcion.id;

    // Act: Cancelar la inscripción
    const result = await inscripcionesService.cancelarInscripcion(
      inscripcion.id,
      'Cancelación de prueba'
    );

    // Assert: Verificar que se canceló correctamente
    expect(result.estado).toBe(EstadoInscripcion.CANCELADA);
    expect(result.comentarios).toBe('Cancelación de prueba');

    logger.log(`Inscripción cancelada: ${result.id}`);
  });

  // Test de estadísticas de inscripciones por liga
  it('debe obtener estadísticas correctas de inscripciones por liga', async () => {
    // Arrange: Crear múltiples usuarios e inscripciones
    const usuario2Data = generateTestUser('inscripcion2');
    const usuario2 = await UsuariosTestHelper.createTestUser(usuario2Data);

    const usuario3Data = generateTestUser('inscripcion3');
    const usuario3 = await UsuariosTestHelper.createTestUser(usuario3Data);

    // Crear inscripciones con diferentes estados
    const inscripcion1 = await InscripcionesTestHelper.createTestInscripcion(
      testUserId!,
      testLigaId!,
      'RX-Masculino'
    );

    const inscripcion2 = await InscripcionesTestHelper.createTestInscripcion(
      usuario2.id,
      testLigaId!,
      'RX-Femenino'
    );

    const inscripcion3 = await InscripcionesTestHelper.createTestInscripcion(
      usuario3.id,
      testLigaId!,
      'Intermedio-Masculino'
    );

    // Confirmar pago de una inscripción
    await InscripcionesTestHelper.confirmarPagoInscripcion(inscripcion2.id);

    // Cancelar una inscripción
    await InscripcionesTestHelper.cancelarInscripcion(inscripcion3.id);

    // Act: Obtener estadísticas
    const stats = await InscripcionesTestHelper.getEstadisticasInscripcionesPorLiga(testLigaId!);

    // Assert: Verificar estadísticas
    expect(stats.total).toBe(3);
    expect(stats.pendientes).toBe(1); // inscripcion1
    expect(stats.pagadas).toBe(1);    // inscripcion2
    expect(stats.canceladas).toBe(1); // inscripcion3
    expect(stats.rechazadas).toBe(0);

    logger.log(`Estadísticas de liga: ${JSON.stringify(stats)}`);
  });
}
