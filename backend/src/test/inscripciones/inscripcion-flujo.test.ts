import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InscripcionesService } from '../../modules/inscripciones/inscripciones.service';
import { LigasService } from '../../modules/ligas/ligas.service';
import { UsuariosService } from '../../modules/usuarios/usuarios.service';
import { Inscripcion, EstadoInscripcion, MetodoPago } from '../../modules/inscripciones/entities/inscripcion.entity';
import { Liga, EstadoLiga } from '../../modules/ligas/entities/liga.entity';
import { Usuario, NivelUsuario, GeneroUsuario } from '../../modules/usuarios/entities/usuario.entity';
import { Box } from '../../modules/boxes/entities/box.entity';

/**
 * Test del flujo completo de inscripción a liga
 */
describe('Flujo de Inscripción a Liga', () => {
  let inscripcionesService: InscripcionesService;
  let ligasService: LigasService;
  let usuariosService: UsuariosService;
  let module: TestingModule;
  let testUser: Usuario;
  let testLiga: Liga;
  let testInscripcion: Inscripcion;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'postgres',
          host: process.env.DB_HOST || 'localhost',
          port: parseInt(process.env.DB_PORT || '5432', 10),
          username: process.env.DB_USERNAME || 'postgres',
          password: process.env.DB_PASSWORD || 'postgres',
          database: process.env.DB_DATABASE || 'wodleague',
          entities: [Inscripcion, Liga, Usuario, Box],
          synchronize: false,
          logging: false,
        }),
        TypeOrmModule.forFeature([Inscripcion, Liga, Usuario, Box]),
      ],
      providers: [InscripcionesService, LigasService, UsuariosService],
    }).compile();

    inscripcionesService = module.get<InscripcionesService>(InscripcionesService);
    ligasService = module.get<LigasService>(LigasService);
    usuariosService = module.get<UsuariosService>(UsuariosService);
  });

  beforeEach(async () => {
    // Crear usuario de prueba
    const userData = {
      nombre: 'Usuario Inscripción',
      alias: 'usuario_inscripcion',
      email: `usuario.inscripcion.${Date.now()}@example.com`,
      password: 'TestPassword123!',
      nivel: NivelUsuario.RX,
      genero: GeneroUsuario.MASCULINO,
    };

    testUser = await usuariosService.create(userData);

    // Crear liga de prueba
    const ligaData = {
      nombre: `Liga Inscripción ${Date.now()}`,
      descripcion: 'Liga para pruebas de inscripción',
      fechaInicio: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      fechaFin: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
      duracionSemanas: 8,
      precioInscripcion: 100,
      tieneDescuentoEarly: false,
      categoriaRx: true,
      categoriaIntermedio: true,
      categoriaScaled: true,
      generoMasculino: true,
      generoFemenino: true,
    };

    testLiga = await ligasService.create(ligaData);
  });

  afterEach(async () => {
    // Limpiar datos de prueba
    try {
      if (testInscripcion) {
        await inscripcionesService.remove(testInscripcion.id);
      }
      if (testUser) {
        await usuariosService.remove(testUser.id);
      }
      if (testLiga) {
        await ligasService.remove(testLiga.id);
      }
    } catch (error) {
      console.log('Error en limpieza:', error.message);
    }
  });

  afterAll(async () => {
    await module.close();
  });

  it('debe crear una inscripción exitosamente', async () => {
    // Arrange
    const createInscripcionDto = {
      usuarioId: testUser.id,
      ligaId: testLiga.id,
      fechaInscripcion: new Date(),
      categoria: 'RX-Masculino',
    };

    // Act
    testInscripcion = await inscripcionesService.create(createInscripcionDto);

    // Assert
    expect(testInscripcion).toBeDefined();
    expect(testInscripcion.id).toBeDefined();
    expect(testInscripcion.usuarioId).toBe(testUser.id);
    expect(testInscripcion.ligaId).toBe(testLiga.id);
    expect(testInscripcion.estado).toBe(EstadoInscripcion.PENDIENTE);
    expect(testInscripcion.categoria).toBe('RX-Masculino');
    expect(Number(testInscripcion.montoPagado)).toBe(0);
  });

  it('debe confirmar el pago de una inscripción', async () => {
    // Arrange: Crear inscripción
    const createInscripcionDto = {
      usuarioId: testUser.id,
      ligaId: testLiga.id,
      fechaInscripcion: new Date(),
      categoria: 'RX-Masculino',
    };

    testInscripcion = await inscripcionesService.create(createInscripcionDto);

    const confirmarPagoDto = {
      fechaPago: new Date(),
      metodoPago: MetodoPago.TRANSFERENCIA,
      referenciaPago: 'REF-TEST-123',
      montoPagado: 100,
      comentarios: 'Pago confirmado en test',
    };

    // Act
    const inscripcionPagada = await inscripcionesService.confirmarPago(
      testInscripcion.id,
      confirmarPagoDto
    );

    // Assert
    expect(inscripcionPagada.estado).toBe(EstadoInscripcion.PAGADA);
    expect(inscripcionPagada.fechaPago).toBeDefined();
    expect(inscripcionPagada.metodoPago).toBe(MetodoPago.TRANSFERENCIA);
    expect(inscripcionPagada.referenciaPago).toBe('REF-TEST-123');
    expect(inscripcionPagada.montoPagado).toBe(100);
  });

  it('debe buscar inscripciones por usuario', async () => {
    // Arrange: Crear inscripción
    const createInscripcionDto = {
      usuarioId: testUser.id,
      ligaId: testLiga.id,
      fechaInscripcion: new Date(),
      categoria: 'RX-Masculino',
    };

    testInscripcion = await inscripcionesService.create(createInscripcionDto);

    // Act
    const inscripciones = await inscripcionesService.findByUsuario(testUser.id);

    // Assert
    expect(inscripciones).toBeDefined();
    expect(inscripciones.length).toBeGreaterThan(0);
    expect(inscripciones.some(i => i.id === testInscripcion.id)).toBe(true);
  });

  it('debe buscar inscripciones por liga', async () => {
    // Arrange: Crear inscripción
    const createInscripcionDto = {
      usuarioId: testUser.id,
      ligaId: testLiga.id,
      fechaInscripcion: new Date(),
      categoria: 'RX-Masculino',
    };

    testInscripcion = await inscripcionesService.create(createInscripcionDto);

    // Act
    const inscripciones = await inscripcionesService.findByLiga(testLiga.id);

    // Assert
    expect(inscripciones).toBeDefined();
    expect(inscripciones.length).toBeGreaterThan(0);
    expect(inscripciones.some(i => i.id === testInscripcion.id)).toBe(true);
  });

  it('debe cancelar una inscripción pendiente', async () => {
    // Arrange: Crear inscripción
    const createInscripcionDto = {
      usuarioId: testUser.id,
      ligaId: testLiga.id,
      fechaInscripcion: new Date(),
      categoria: 'RX-Masculino',
    };

    testInscripcion = await inscripcionesService.create(createInscripcionDto);

    // Act
    const inscripcionCancelada = await inscripcionesService.cancelarInscripcion(
      testInscripcion.id,
      'Cancelación de prueba'
    );

    // Assert
    expect(inscripcionCancelada.estado).toBe(EstadoInscripcion.CANCELADA);
    expect(inscripcionCancelada.comentarios).toBe('Cancelación de prueba');
  });

  it('debe rechazar inscripción duplicada', async () => {
    // Arrange: Crear primera inscripción
    const createInscripcionDto = {
      usuarioId: testUser.id,
      ligaId: testLiga.id,
      fechaInscripcion: new Date(),
      categoria: 'RX-Masculino',
    };

    testInscripcion = await inscripcionesService.create(createInscripcionDto);

    // Act & Assert: Intentar crear segunda inscripción debe fallar
    await expect(
      inscripcionesService.create(createInscripcionDto)
    ).rejects.toThrow();
  });

  it('debe obtener estadísticas de inscripciones por liga', async () => {
    // Arrange: Crear inscripción
    const createInscripcionDto = {
      usuarioId: testUser.id,
      ligaId: testLiga.id,
      fechaInscripcion: new Date(),
      categoria: 'RX-Masculino',
    };

    testInscripcion = await inscripcionesService.create(createInscripcionDto);

    // Act
    const stats = await inscripcionesService.contarInscripcionesPorLiga(testLiga.id);

    // Assert
    expect(stats).toBeDefined();
    expect(stats.total).toBeGreaterThan(0);
    expect(stats.pendientes).toBeGreaterThan(0);
  });
});
