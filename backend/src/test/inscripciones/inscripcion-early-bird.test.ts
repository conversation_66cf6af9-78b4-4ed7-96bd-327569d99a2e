import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InscripcionesService } from '../../modules/inscripciones/inscripciones.service';
import { LigasService } from '../../modules/ligas/ligas.service';
import { UsuariosService } from '../../modules/usuarios/usuarios.service';
import { Inscripcion, EstadoInscripcion } from '../../modules/inscripciones/entities/inscripcion.entity';
import { Liga, EstadoLiga } from '../../modules/ligas/entities/liga.entity';
import { Usuario, NivelUsuario, GeneroUsuario } from '../../modules/usuarios/entities/usuario.entity';
import { Box } from '../../modules/boxes/entities/box.entity';
import { ConflictException } from '@nestjs/common';

/**
 * Test específico para descuento early bird y validaciones de liga
 */
describe('Inscripción Early Bird y Validaciones', () => {
  let inscripcionesService: InscripcionesService;
  let ligasService: LigasService;
  let usuariosService: UsuariosService;
  let module: TestingModule;
  let testUser: Usuario;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'postgres',
          host: process.env.DB_HOST || 'localhost',
          port: parseInt(process.env.DB_PORT || '5432', 10),
          username: process.env.DB_USERNAME || 'postgres',
          password: process.env.DB_PASSWORD || 'postgres',
          database: process.env.DB_DATABASE || 'wodleague',
          entities: [Inscripcion, Liga, Usuario, Box],
          synchronize: false,
          logging: false,
        }),
        TypeOrmModule.forFeature([Inscripcion, Liga, Usuario, Box]),
      ],
      providers: [InscripcionesService, LigasService, UsuariosService],
    }).compile();

    inscripcionesService = module.get<InscripcionesService>(InscripcionesService);
    ligasService = module.get<LigasService>(LigasService);
    usuariosService = module.get<UsuariosService>(UsuariosService);

    // Crear usuario de prueba que se reutilizará
    const userData = {
      nombre: 'Usuario Early Bird',
      alias: 'usuario_early_bird',
      email: `usuario.early.bird.${Date.now()}@example.com`,
      password: 'TestPassword123!',
      nivel: NivelUsuario.RX,
      genero: GeneroUsuario.MASCULINO,
    };

    testUser = await usuariosService.create(userData);
  });

  afterAll(async () => {
    // Limpiar usuario de prueba
    try {
      await usuariosService.remove(testUser.id);
    } catch (error) {
      console.log('Error al limpiar usuario:', error.message);
    }
    await module.close();
  });

  it('debe aplicar descuento early bird cuando la liga comienza en más de un mes', async () => {
    // Arrange: Crear liga que comienza en 45 días (más de un mes)
    const fechaInicio = new Date();
    fechaInicio.setDate(fechaInicio.getDate() + 45);
    
    const fechaFin = new Date(fechaInicio);
    fechaFin.setDate(fechaFin.getDate() + 56); // 8 semanas después

    const ligaData = {
      nombre: `Liga Early Bird ${Date.now()}`,
      descripcion: 'Liga con descuento early bird',
      fechaInicio,
      fechaFin,
      duracionSemanas: 8,
      precioInscripcion: 150,
      tieneDescuentoEarly: true,
      precioEarly: 120,
      categoriaRx: true,
      categoriaIntermedio: true,
      categoriaScaled: true,
      generoMasculino: true,
      generoFemenino: true,
    };

    const liga = await ligasService.create(ligaData);

    const createInscripcionDto = {
      usuarioId: testUser.id,
      ligaId: liga.id,
      fechaInscripcion: new Date(),
      categoria: 'RX-Masculino',
    };

    // Act
    const inscripcion = await inscripcionesService.create(createInscripcionDto);

    // Assert
    expect(inscripcion.esEarly).toBe(true);

    // Cleanup
    await inscripcionesService.remove(inscripcion.id);
    await ligasService.remove(liga.id);
  });

  it('NO debe aplicar descuento early bird cuando la liga comienza en menos de un mes', async () => {
    // Arrange: Crear liga que comienza en 20 días (menos de un mes)
    const fechaInicio = new Date();
    fechaInicio.setDate(fechaInicio.getDate() + 20);
    
    const fechaFin = new Date(fechaInicio);
    fechaFin.setDate(fechaFin.getDate() + 56); // 8 semanas después

    const ligaData = {
      nombre: `Liga No Early Bird ${Date.now()}`,
      descripcion: 'Liga sin descuento early bird',
      fechaInicio,
      fechaFin,
      duracionSemanas: 8,
      precioInscripcion: 150,
      tieneDescuentoEarly: true,
      precioEarly: 120,
      categoriaRx: true,
      categoriaIntermedio: true,
      categoriaScaled: true,
      generoMasculino: true,
      generoFemenino: true,
    };

    const liga = await ligasService.create(ligaData);

    const createInscripcionDto = {
      usuarioId: testUser.id,
      ligaId: liga.id,
      fechaInscripcion: new Date(),
      categoria: 'RX-Masculino',
    };

    // Act
    const inscripcion = await inscripcionesService.create(createInscripcionDto);

    // Assert
    expect(inscripcion.esEarly).toBe(false);

    // Cleanup
    await inscripcionesService.remove(inscripcion.id);
    await ligasService.remove(liga.id);
  });

  it('debe rechazar inscripción en liga finalizada', async () => {
    // Arrange: Crear liga finalizada
    const fechaInicio = new Date();
    fechaInicio.setDate(fechaInicio.getDate() - 70); // Comenzó hace 70 días
    
    const fechaFin = new Date(fechaInicio);
    fechaFin.setDate(fechaFin.getDate() + 56); // Duró 8 semanas

    const ligaData = {
      nombre: `Liga Finalizada ${Date.now()}`,
      descripcion: 'Liga finalizada para pruebas',
      fechaInicio,
      fechaFin,
      duracionSemanas: 8,
      estado: EstadoLiga.FINALIZADA,
      precioInscripcion: 100,
      tieneDescuentoEarly: false,
      categoriaRx: true,
      categoriaIntermedio: true,
      categoriaScaled: true,
      generoMasculino: true,
      generoFemenino: true,
    };

    const liga = await ligasService.create(ligaData);

    const createInscripcionDto = {
      usuarioId: testUser.id,
      ligaId: liga.id,
      fechaInscripcion: new Date(),
      categoria: 'RX-Masculino',
    };

    // Act & Assert
    await expect(
      inscripcionesService.create(createInscripcionDto)
    ).rejects.toThrow(ConflictException);

    // Cleanup
    await ligasService.remove(liga.id);
  });

  it('debe permitir inscripción en liga activa', async () => {
    // Arrange: Crear liga activa
    const fechaInicio = new Date();
    fechaInicio.setDate(fechaInicio.getDate() - 7); // Comenzó hace 7 días
    
    const fechaFin = new Date(fechaInicio);
    fechaFin.setDate(fechaFin.getDate() + 56); // Dura 8 semanas

    const ligaData = {
      nombre: `Liga Activa ${Date.now()}`,
      descripcion: 'Liga activa para pruebas',
      fechaInicio,
      fechaFin,
      duracionSemanas: 8,
      estado: EstadoLiga.ACTIVA,
      precioInscripcion: 100,
      tieneDescuentoEarly: false,
      categoriaRx: true,
      categoriaIntermedio: true,
      categoriaScaled: true,
      generoMasculino: true,
      generoFemenino: true,
    };

    const liga = await ligasService.create(ligaData);

    const createInscripcionDto = {
      usuarioId: testUser.id,
      ligaId: liga.id,
      fechaInscripcion: new Date(),
      categoria: 'RX-Masculino',
    };

    // Act
    const inscripcion = await inscripcionesService.create(createInscripcionDto);

    // Assert
    expect(inscripcion).toBeDefined();
    expect(inscripcion.estado).toBe(EstadoInscripcion.PENDIENTE);

    // Cleanup
    await inscripcionesService.remove(inscripcion.id);
    await ligasService.remove(liga.id);
  });

  it('debe permitir inscripción en liga en preparación', async () => {
    // Arrange: Crear liga en preparación
    const fechaInicio = new Date();
    fechaInicio.setDate(fechaInicio.getDate() + 30); // Comienza en 30 días
    
    const fechaFin = new Date(fechaInicio);
    fechaFin.setDate(fechaFin.getDate() + 56); // Dura 8 semanas

    const ligaData = {
      nombre: `Liga Preparación ${Date.now()}`,
      descripcion: 'Liga en preparación para pruebas',
      fechaInicio,
      fechaFin,
      duracionSemanas: 8,
      estado: EstadoLiga.PREPARACION,
      precioInscripcion: 100,
      tieneDescuentoEarly: false,
      categoriaRx: true,
      categoriaIntermedio: true,
      categoriaScaled: true,
      generoMasculino: true,
      generoFemenino: true,
    };

    const liga = await ligasService.create(ligaData);

    const createInscripcionDto = {
      usuarioId: testUser.id,
      ligaId: liga.id,
      fechaInscripcion: new Date(),
      categoria: 'RX-Masculino',
    };

    // Act
    const inscripcion = await inscripcionesService.create(createInscripcionDto);

    // Assert
    expect(inscripcion).toBeDefined();
    expect(inscripcion.estado).toBe(EstadoInscripcion.PENDIENTE);

    // Cleanup
    await inscripcionesService.remove(inscripcion.id);
    await ligasService.remove(liga.id);
  });
});
