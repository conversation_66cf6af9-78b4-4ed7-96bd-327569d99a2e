import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InscripcionesService } from '../../../modules/inscripciones/inscripciones.service';
import { Inscripcion, EstadoInscripcion } from '../../../modules/inscripciones/entities/inscripcion.entity';
import { CreateInscripcionDto } from '../../../modules/inscripciones/dto/create-inscripcion.dto';
import { ConfirmarPagoDto } from '../../../modules/inscripciones/dto/confirmar-pago.dto';
import { Liga } from '../../../modules/ligas/entities/liga.entity';
import { Usuario } from '../../../modules/usuarios/entities/usuario.entity';
import { LigasService } from '../../../modules/ligas/ligas.service';
import { UsuariosService } from '../../../modules/usuarios/usuarios.service';
import { DatabaseTestHelper } from '../base/database.helper';
import { 
  generateCreateInscripcionDto, 
  generateConfirmacionPago,
  generateCategoria
} from '../fixtures/inscripciones.fixtures';
import { NivelUsuario, GeneroUsuario } from '../../../modules/usuarios/entities/usuario.entity';

/**
 * Helper para tests de Inscripciones
 * 
 * Proporciona funciones utilitarias para facilitar los tests de inscripciones
 */

export class InscripcionesTestHelper {
  private static inscripcionesService: InscripcionesService;
  private static ligasService: LigasService;
  private static usuariosService: UsuariosService;
  private static createdInscripcionIds: string[] = [];

  /**
   * Inicializa el helper con los servicios necesarios
   */
  static async initialize(): Promise<{
    inscripcionesService: InscripcionesService;
    ligasService: LigasService;
    usuariosService: UsuariosService;
  }> {
    if (!this.inscripcionesService) {
      const module: TestingModule = await Test.createTestingModule({
        imports: [
          TypeOrmModule.forRoot(DatabaseTestHelper.getTestDatabaseConfig()),
          TypeOrmModule.forFeature([Inscripcion, Liga, Usuario]),
        ],
        providers: [InscripcionesService, LigasService, UsuariosService],
      }).compile();

      this.inscripcionesService = module.get<InscripcionesService>(InscripcionesService);
      this.ligasService = module.get<LigasService>(LigasService);
      this.usuariosService = module.get<UsuariosService>(UsuariosService);
    }

    return {
      inscripcionesService: this.inscripcionesService,
      ligasService: this.ligasService,
      usuariosService: this.usuariosService,
    };
  }

  /**
   * Crea una inscripción de prueba
   */
  static async createTestInscripcion(
    usuarioId: string,
    ligaId: string,
    categoria?: string
  ): Promise<Inscripcion> {
    const createInscripcionDto = generateCreateInscripcionDto(usuarioId, ligaId, categoria);
    const inscripcion = await this.inscripcionesService.create(createInscripcionDto);
    this.createdInscripcionIds.push(inscripcion.id);
    return inscripcion;
  }

  /**
   * Crea múltiples inscripciones de prueba
   */
  static async createMultipleTestInscripciones(
    usuarioIds: string[],
    ligaId: string
  ): Promise<Inscripcion[]> {
    const inscripciones: Inscripcion[] = [];
    
    for (let i = 0; i < usuarioIds.length; i++) {
      const categoria = i % 2 === 0 ? 'RX-Masculino' : 'RX-Femenino';
      const inscripcion = await this.createTestInscripcion(usuarioIds[i], ligaId, categoria);
      inscripciones.push(inscripcion);
    }
    
    return inscripciones;
  }

  /**
   * Confirma el pago de una inscripción
   */
  static async confirmarPagoInscripcion(
    inscripcionId: string,
    montoPagado: number = 100
  ): Promise<Inscripcion> {
    const confirmarPagoDto = generateConfirmacionPago(montoPagado);
    return this.inscripcionesService.confirmarPago(inscripcionId, confirmarPagoDto);
  }

  /**
   * Cancela una inscripción
   */
  static async cancelarInscripcion(
    inscripcionId: string,
    comentarios?: string
  ): Promise<Inscripcion> {
    return this.inscripcionesService.cancelarInscripcion(inscripcionId, comentarios);
  }

  /**
   * Busca inscripciones por usuario
   */
  static async findInscripcionesByUsuario(usuarioId: string): Promise<Inscripcion[]> {
    return this.inscripcionesService.findByUsuario(usuarioId);
  }

  /**
   * Busca inscripciones por liga
   */
  static async findInscripcionesByLiga(ligaId: string): Promise<Inscripcion[]> {
    return this.inscripcionesService.findByLiga(ligaId);
  }

  /**
   * Busca inscripción específica por usuario y liga
   */
  static async findInscripcionByUsuarioAndLiga(
    usuarioId: string,
    ligaId: string
  ): Promise<Inscripcion> {
    return this.inscripcionesService.findByUsuarioAndLiga(usuarioId, ligaId);
  }

  /**
   * Busca inscripciones por estado
   */
  static async findInscripcionesByEstado(estado: EstadoInscripcion): Promise<Inscripcion[]> {
    return this.inscripcionesService.findByEstado(estado);
  }

  /**
   * Obtiene estadísticas de inscripciones por liga
   */
  static async getEstadisticasInscripcionesPorLiga(ligaId: string): Promise<{
    total: number;
    pagadas: number;
    pendientes: number;
    canceladas: number;
    rechazadas: number;
  }> {
    return this.inscripcionesService.contarInscripcionesPorLiga(ligaId);
  }

  /**
   * Genera categoría basada en usuario
   */
  static generateCategoriaFromUsuario(usuario: Usuario): string {
    if (!usuario.nivel || !usuario.genero) {
      return 'RX-Masculino'; // Categoría por defecto
    }
    return generateCategoria(usuario.nivel, usuario.genero);
  }

  /**
   * Verifica si un usuario puede inscribirse en una liga
   */
  static async canUserRegisterInLiga(usuarioId: string, ligaId: string): Promise<boolean> {
    try {
      // Verificar si ya existe una inscripción
      const existingInscripcion = await this.inscripcionesService.findByUsuarioAndLiga(
        usuarioId,
        ligaId
      );
      return !existingInscripcion;
    } catch (error) {
      // Si no encuentra la inscripción, el usuario puede inscribirse
      return true;
    }
  }

  /**
   * Simula el flujo completo de inscripción y pago
   */
  static async simulateCompleteRegistrationFlow(
    usuarioId: string,
    ligaId: string,
    categoria?: string
  ): Promise<{
    inscripcion: Inscripcion;
    inscripcionPagada: Inscripcion;
  }> {
    // 1. Crear inscripción
    const inscripcion = await this.createTestInscripcion(usuarioId, ligaId, categoria);
    
    // 2. Confirmar pago
    const inscripcionPagada = await this.confirmarPagoInscripcion(inscripcion.id);
    
    return { inscripcion, inscripcionPagada };
  }

  /**
   * Limpia todas las inscripciones creadas durante los tests
   */
  static async cleanup(): Promise<void> {
    if (this.inscripcionesService && this.createdInscripcionIds.length > 0) {
      for (const inscripcionId of this.createdInscripcionIds) {
        try {
          await this.inscripcionesService.remove(inscripcionId);
        } catch (error) {
          // Ignorar errores de limpieza
          console.warn(`Error al limpiar inscripción ${inscripcionId}:`, error.message);
        }
      }
      this.createdInscripcionIds = [];
    }
  }

  /**
   * Obtiene el servicio de inscripciones para uso directo en tests
   */
  static getService(): InscripcionesService {
    return this.inscripcionesService;
  }
}
