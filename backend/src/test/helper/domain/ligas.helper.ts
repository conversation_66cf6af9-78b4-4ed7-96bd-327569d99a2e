import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LigasService } from '../../../modules/ligas/ligas.service';
import { Liga } from '../../../modules/ligas/entities/liga.entity';
import { CreateLigaDto } from '../../../modules/ligas/dto/create-liga.dto';
import { DatabaseTestHelper } from '../base/database.helper';
import { generateCreateLigaDto, generateTestLiga } from '../fixtures/ligas.fixtures';

/**
 * Helper para tests de Ligas
 * 
 * Proporciona funciones utilitarias para facilitar los tests de ligas
 */

export class LigasTestHelper {
  private static ligasService: LigasService;
  private static createdLigaIds: string[] = [];

  /**
   * Inicializa el helper con el servicio de ligas
   */
  static async initialize(): Promise<LigasService> {
    if (!this.ligasService) {
      const dbConfig = DatabaseTestHelper.getTestDatabaseConfig();
      const module: TestingModule = await Test.createTestingModule({
        imports: [
          TypeOrmModule.forRoot({
            ...dbConfig,
            entities: [Liga],
          }),
          TypeOrmModule.forFeature([Liga]),
        ],
        providers: [LigasService],
      }).compile();

      this.ligasService = module.get<LigasService>(LigasService);
    }

    return this.ligasService;
  }

  /**
   * Crea una liga de prueba
   */
  static async createTestLiga(suffix: string = ''): Promise<Liga> {
    const createLigaDto = generateCreateLigaDto(suffix);
    const liga = await this.ligasService.create(createLigaDto);
    this.createdLigaIds.push(liga.id);
    return liga;
  }

  /**
   * Crea múltiples ligas de prueba
   */
  static async createMultipleTestLigas(count: number): Promise<Liga[]> {
    const ligas: Liga[] = [];
    for (let i = 0; i < count; i++) {
      const liga = await this.createTestLiga(`${i + 1}`);
      ligas.push(liga);
    }
    return ligas;
  }

  /**
   * Crea una liga activa para tests de inscripción
   */
  static async createLigaActiva(suffix: string = ''): Promise<Liga> {
    const ligaData = generateTestLiga(suffix);
    
    // Modificar fechas para que sea una liga activa
    const fechaInicio = new Date();
    fechaInicio.setDate(fechaInicio.getDate() - 7); // Comenzó hace 7 días
    
    const fechaFin = new Date(fechaInicio);
    fechaFin.setDate(fechaFin.getDate() + (8 * 7)); // Dura 8 semanas
    
    const createLigaDto: CreateLigaDto = {
      ...ligaData,
      fechaInicio,
      fechaFin,
      estado: 'Activa' as any, // Forzar estado activo
    };

    const liga = await this.ligasService.create(createLigaDto);
    this.createdLigaIds.push(liga.id);
    return liga;
  }

  /**
   * Crea una liga finalizada para tests de validación
   */
  static async createLigaFinalizada(suffix: string = ''): Promise<Liga> {
    const ligaData = generateTestLiga(suffix);
    
    // Modificar fechas para que sea una liga finalizada
    const fechaInicio = new Date();
    fechaInicio.setDate(fechaInicio.getDate() - 70); // Comenzó hace 70 días
    
    const fechaFin = new Date(fechaInicio);
    fechaFin.setDate(fechaFin.getDate() + (8 * 7)); // Duró 8 semanas
    
    const createLigaDto: CreateLigaDto = {
      ...ligaData,
      fechaInicio,
      fechaFin,
      estado: 'Finalizada' as any, // Forzar estado finalizado
    };

    const liga = await this.ligasService.create(createLigaDto);
    this.createdLigaIds.push(liga.id);
    return liga;
  }

  /**
   * Crea una liga con descuento early bird
   */
  static async createLigaConDescuentoEarly(suffix: string = ''): Promise<Liga> {
    const ligaData = generateTestLiga(suffix);
    
    // Modificar fechas para que aplique descuento early bird
    const fechaInicio = new Date();
    fechaInicio.setDate(fechaInicio.getDate() + 45); // Comienza en 45 días (más de un mes)
    
    const fechaFin = new Date(fechaInicio);
    fechaFin.setDate(fechaFin.getDate() + (8 * 7)); // Dura 8 semanas
    
    const createLigaDto: CreateLigaDto = {
      ...ligaData,
      fechaInicio,
      fechaFin,
      precioInscripcion: 150,
      tieneDescuentoEarly: true,
      precioEarly: 120,
    };

    const liga = await this.ligasService.create(createLigaDto);
    this.createdLigaIds.push(liga.id);
    return liga;
  }

  /**
   * Busca una liga por ID
   */
  static async findLigaById(id: string): Promise<Liga> {
    return this.ligasService.findOne(id);
  }

  /**
   * Obtiene todas las ligas activas
   */
  static async findLigasActivas(): Promise<Liga[]> {
    return this.ligasService.findActivas();
  }

  /**
   * Limpia todas las ligas creadas durante los tests
   */
  static async cleanup(): Promise<void> {
    if (this.ligasService && this.createdLigaIds.length > 0) {
      for (const ligaId of this.createdLigaIds) {
        try {
          await this.ligasService.remove(ligaId);
        } catch (error) {
          // Ignorar errores de limpieza
          console.warn(`Error al limpiar liga ${ligaId}:`, error.message);
        }
      }
      this.createdLigaIds = [];
    }
  }

  /**
   * Obtiene el servicio de ligas para uso directo en tests
   */
  static getService(): LigasService {
    return this.ligasService;
  }
}
