import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Logger } from '@nestjs/common';
import { Repository } from 'typeorm';
import { Usuario } from '../../../modules/usuarios/entities/usuario.entity';
import { Box } from '../../../modules/boxes/entities/box.entity';
import { UsuariosService } from '../../../modules/usuarios/usuarios.service';
import { DatabaseTestHelper } from '../base/database.helper';
import { usuariosFixtures } from '../fixtures/usuarios.fixtures';
import { generateTestUser } from '../fixtures/usuarios.fixtures';
import * as bcrypt from 'bcrypt';

/**
 * Helper especializado para pruebas de usuarios
 * 
 * Proporciona métodos específicos para testing de usuario
 * que facilitan la creación, búsqueda y limpieza de usuarios de prueba
 */
export class UsuariosTestHelper {
  private static readonly logger = new Logger(UsuariosTestHelper.name);
  private static usuariosService: UsuariosService;
  private static createdUserIds: string[] = [];

  /**
   * Inicializa el helper con el servicio de usuarios
   */
  static async initialize(): Promise<UsuariosService> {
    if (!this.usuariosService) {
      const dbConfig = DatabaseTestHelper.getTestDatabaseConfig();
      const module: TestingModule = await Test.createTestingModule({
        imports: [
          TypeOrmModule.forRoot({
            ...dbConfig,
            entities: [Usuario, Box],
          }),
          TypeOrmModule.forFeature([Usuario, Box]),
        ],
        providers: [UsuariosService],
      }).compile();

      this.usuariosService = module.get<UsuariosService>(UsuariosService);
    }

    return this.usuariosService;
  }

  /**
   * Crea un módulo de testing con los servicios necesarios para usuarios
   *
   * @returns Módulo de testing configurado para usuarios
   */
  static async createUsuariosTestingModule(): Promise<TestingModule> {
    return await DatabaseTestHelper.createTestingModule(
      [Usuario, Box], // Incluimos Box para resolver la relación con Usuario
      [UsuariosService]
    );
  }
  
  /**
   * Crea un usuario de prueba usando el servicio
   *
   * @param userData Datos del usuario
   * @returns Usuario creado
   */
  static async createTestUser(userData: any): Promise<Usuario> {
    try {
      const user = await this.usuariosService.create(userData);
      this.createdUserIds.push(user.id);
      this.logger.log(`Usuario de prueba creado: ${user.email} (ID: ${user.id})`);
      return user;
    } catch (error) {
      this.logger.error(`Error al crear usuario de prueba: ${error.message}`);
      throw error;
    }
  }

  /**
   * Crea un usuario de prueba en la base de datos (método legacy)
   *
   * @param usuariosRepository Repositorio de usuarios
   * @param userData Datos del usuario (opcional, usa defaultUser por defecto)
   * @returns Usuario creado
   */
  static async createTestUserWithRepository(
    usuariosRepository: Repository<Usuario>,
    userData = { ...usuariosFixtures.defaultUser }
  ): Promise<Usuario> {
    try {
      // Hashear la contraseña
      const hashedPassword = await bcrypt.hash(userData.password, 10);

      // Crear el usuario con la contraseña hasheada
      const newUser = usuariosRepository.create({
        ...userData,
        password: hashedPassword
      });

      // Guardar y devolver el usuario
      const createdUser = await usuariosRepository.save(newUser);
      this.logger.log(`Usuario de prueba creado: ${createdUser.email} (ID: ${createdUser.id})`);

      return createdUser;
    } catch (error) {
      this.logger.error(`Error al crear usuario de prueba: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Elimina un usuario de prueba de la base de datos
   * 
   * @param usuariosRepository Repositorio de usuarios
   * @param criteria Criterios para encontrar el usuario (id, email, etc.)
   */
  static async deleteTestUser(
    usuariosRepository: Repository<Usuario>,
    criteria: any
  ): Promise<void> {
    try {
      await DatabaseTestHelper.cleanTestData(usuariosRepository, criteria);
      this.logger.log(`Usuario de prueba eliminado según criterio: ${JSON.stringify(criteria)}`);
    } catch (error) {
      this.logger.error(`Error al eliminar usuario de prueba: ${error.message}`);
    }
  }
  
  /**
   * Elimina todos los usuarios de prueba de la base de datos
   * basándose en un patrón de correo electrónico
   * 
   * @param usuariosRepository Repositorio de usuarios
   * @param emailPattern Patrón de correo (por defecto '%.example.com')
   */
  static async cleanupAllTestUsers(
    usuariosRepository: Repository<Usuario>,
    emailPattern: string = '%.example.com'
  ): Promise<void> {
    try {
      const usersToDelete = await usuariosRepository
        .createQueryBuilder('usuario')
        .where('usuario.email LIKE :pattern', { pattern: emailPattern })
        .getMany();
      
      for (const user of usersToDelete) {
        await usuariosRepository.remove(user);
      }
      
      this.logger.log(`Se eliminaron ${usersToDelete.length} usuarios de prueba`);
    } catch (error) {
      this.logger.error(`Error al limpiar usuarios de prueba: ${error.message}`);
    }
  }
  
  /**
   * Verifica si las credenciales de un usuario son válidas
   *
   * @param usuariosRepository Repositorio de usuarios
   * @param email Email del usuario
   * @param password Contraseña a verificar
   * @returns true si las credenciales son válidas, false en caso contrario
   */
  static async validateUserCredentials(
    usuariosRepository: Repository<Usuario>,
    email: string,
    password: string
  ): Promise<boolean> {
    try {
      const user = await usuariosRepository.findOne({ where: { email } });

      if (!user) {
        return false;
      }

      return await bcrypt.compare(password, user.password);
    } catch (error) {
      this.logger.error(`Error al validar credenciales: ${error.message}`);
      return false;
    }
  }

  /**
   * Limpia todos los usuarios creados durante los tests
   */
  static async cleanup(): Promise<void> {
    if (this.usuariosService && this.createdUserIds.length > 0) {
      for (const userId of this.createdUserIds) {
        try {
          await this.usuariosService.remove(userId);
        } catch (error) {
          // Ignorar errores de limpieza
          this.logger.warn(`Error al limpiar usuario ${userId}:`, error.message);
        }
      }
      this.createdUserIds = [];
    }
  }

  /**
   * Obtiene el servicio de usuarios para uso directo en tests
   */
  static getService(): UsuariosService {
    return this.usuariosService;
  }
}
