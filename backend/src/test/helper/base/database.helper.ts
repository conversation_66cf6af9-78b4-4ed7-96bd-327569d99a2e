import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { Connection, Repository, ObjectLiteral } from 'typeorm';
import databaseConfig from '../../../config/database.config';
import appConfig from '../../../config/app.config';
import { Logger } from '@nestjs/common';

/**
 * Base Database Helper for Testing
 * 
 * Core functionality for database connection and module setup in tests.
 * This helper serves as the foundation for all database operations in tests.
 */
export class DatabaseTestHelper {
  private static readonly logger = new Logger(DatabaseTestHelper.name);

  /**
   * Check if the database is accessible
   * 
   * @param configPath Path to env config file
   * @returns {Promise<boolean>} True if connection is successful, false otherwise
   */
  static async checkDatabaseConnection(
    configPath: string = '.env.development'
  ): Promise<boolean> {
    try {
      const module: TestingModule = await Test.createTestingModule({
        imports: [
          ConfigModule.forRoot({
            isGlobal: true,
            load: [appConfig, databaseConfig],
            envFilePath: configPath,
          }),
          TypeOrmModule.forRootAsync({
            inject: [ConfigService],
            useFactory: (configService: ConfigService) => ({
              type: 'postgres',
              host: configService.get('database.host'),
              port: configService.get('database.port'),
              username: configService.get('database.username'),
              password: configService.get('database.password'),
              database: configService.get('database.database'),
              entities: [],
              synchronize: false,
            }),
          }),
        ],
      }).compile();

      const connection = module.get(Connection);
      const configService = module.get(ConfigService);
      await connection.query('SELECT 1'); // Simple query to verify connection

      this.logger.log(`Successfully connected to database: ${configService.get('database.database')}`);

      await module.close();
      return true;
    } catch (error) {
      this.logger.error(`Failed to connect to database: ${error.message}`);
      return false;
    }
  }

  /**
   * Create a test module with all necessary entities and database connection
   * 
   * @param entities Additional entities to include
   * @param providers Array of providers to include in the testing module
   * @param configPath Path to env config file
   * @returns TestingModule with database connection
   */
  static async createTestingModule(
    entities: any[] = [],
    providers: any[] = [],
    configPath: string = '.env.development'
  ): Promise<TestingModule> {
    // Usar solo las entidades proporcionadas
    const allEntities = [...entities];

    return Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          load: [appConfig, databaseConfig],
          envFilePath: configPath,
        }),
        TypeOrmModule.forRootAsync({
          inject: [ConfigService],
          useFactory: (configService: ConfigService) => ({
            ...configService.get('database'),
            entities: allEntities,
            synchronize: true,
          }),
        }),
        TypeOrmModule.forFeature(allEntities),
      ],
      providers: providers,
    }).compile();
  }

  /**
   * Get test database configuration
   *
   * @param configPath Path to env config file
   * @returns Database configuration object for tests
   */
  static getTestDatabaseConfig(configPath: string = '.env.development') {
    return {
      type: 'postgres' as const,
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432', 10),
      username: process.env.DB_USERNAME || 'postgres',
      password: process.env.DB_PASSWORD || 'postgres',
      database: process.env.DB_DATABASE || 'wodleague',
      synchronize: true, // Para tests, permitir sincronización automática
      dropSchema: false, // No eliminar esquema en tests
      logging: false, // Desactivar logging en tests
      entities: [], // Se configurará dinámicamente
    };
  }

  /**
   * Clean up test data by specific criteria
   *
   * @param repository Repository to clean
   * @param criteria Object with criteria for deletion (e.g., { email: '<EMAIL>' })
   */
  static async cleanTestData<T extends ObjectLiteral>(
    repository: Repository<T>,
    criteria: any
  ): Promise<void> {
    try {
      // Find entities matching criteria
      const entities = await repository.find({ where: criteria });

      // Delete each entity
      for (const entity of entities) {
        await repository.remove(entity);
      }

      this.logger.log(`Cleaned up ${entities.length} test entities`);
    } catch (error) {
      this.logger.error(`Failed to clean test data: ${error.message}`);
    }
  }
}
