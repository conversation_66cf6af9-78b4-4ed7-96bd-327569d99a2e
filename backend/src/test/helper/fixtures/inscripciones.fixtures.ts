import { Inscripcion, EstadoInscripcion, MetodoPago } from '../../../modules/inscripciones/entities/inscripcion.entity';
import { CreateInscripcionDto } from '../../../modules/inscripciones/dto/create-inscripcion.dto';
import { NivelUsuario, GeneroUsuario } from '../../../modules/usuarios/entities/usuario.entity';

/**
 * Fixtures para tests de Inscripciones
 * 
 * Proporciona datos de prueba consistentes para los tests de inscripciones
 */

export interface TestInscripcionData {
  usuarioId: string;
  ligaId: string;
  fechaInscripcion: Date;
  estado: EstadoInscripcion;
  fechaPago?: Date;
  metodoPago?: MetodoPago;
  referenciaPago?: string;
  montoPagado?: number;
  esEarly?: boolean;
  categoria: string;
  comentarios?: string;
}

/**
 * Genera datos de prueba para una inscripción
 */
export function generateTestInscripcion(
  usuarioId: string,
  ligaId: string,
  suffix: string = ''
): TestInscripcionData {
  return {
    usuarioId,
    ligaId,
    fechaInscripcion: new Date(),
    estado: EstadoInscripcion.PENDIENTE,
    categoria: 'RX-Masculino',
    comentarios: `Inscripción de prueba ${suffix}`,
  };
}

/**
 * Genera DTO para crear una inscripción
 */
export function generateCreateInscripcionDto(
  usuarioId: string,
  ligaId: string,
  categoria: string = 'RX-Masculino'
): CreateInscripcionDto {
  return {
    usuarioId,
    ligaId,
    fechaInscripcion: new Date(),
    categoria,
  };
}

/**
 * Genera inscripción pagada para tests
 */
export function generateInscripcionPagada(
  usuarioId: string,
  ligaId: string,
  suffix: string = ''
): TestInscripcionData {
  return {
    usuarioId,
    ligaId,
    fechaInscripcion: new Date(),
    estado: EstadoInscripcion.PAGADA,
    fechaPago: new Date(),
    metodoPago: MetodoPago.TRANSFERENCIA,
    referenciaPago: `REF-TEST-${suffix}`,
    montoPagado: 100,
    esEarly: false,
    categoria: 'RX-Masculino',
    comentarios: `Inscripción pagada de prueba ${suffix}`,
  };
}

/**
 * Genera inscripción con descuento early bird
 */
export function generateInscripcionEarlyBird(
  usuarioId: string,
  ligaId: string,
  suffix: string = ''
): TestInscripcionData {
  return {
    usuarioId,
    ligaId,
    fechaInscripcion: new Date(),
    estado: EstadoInscripcion.PENDIENTE,
    esEarly: true,
    categoria: 'RX-Masculino',
    comentarios: `Inscripción early bird de prueba ${suffix}`,
  };
}

/**
 * Genera inscripción cancelada para tests
 */
export function generateInscripcionCancelada(
  usuarioId: string,
  ligaId: string,
  suffix: string = ''
): TestInscripcionData {
  return {
    usuarioId,
    ligaId,
    fechaInscripcion: new Date(),
    estado: EstadoInscripcion.CANCELADA,
    categoria: 'RX-Masculino',
    comentarios: `Inscripción cancelada de prueba ${suffix}`,
  };
}

/**
 * Genera categoría basada en nivel y género del usuario
 */
export function generateCategoria(nivel: NivelUsuario, genero: GeneroUsuario): string {
  return `${nivel}-${genero}`;
}

/**
 * Genera múltiples inscripciones para diferentes categorías
 */
export function generateInscripcionesPorCategoria(
  usuarioIds: string[],
  ligaId: string
): CreateInscripcionDto[] {
  const categorias = [
    'RX-Masculino',
    'RX-Femenino',
    'Intermedio-Masculino',
    'Intermedio-Femenino',
    'Scaled-Masculino',
    'Scaled-Femenino',
  ];

  return usuarioIds.map((usuarioId, index) => ({
    usuarioId,
    ligaId,
    fechaInscripcion: new Date(),
    categoria: categorias[index % categorias.length],
  }));
}

/**
 * Datos para test de validación de inscripción duplicada
 */
export function generateInscripcionDuplicada(
  usuarioId: string,
  ligaId: string
): CreateInscripcionDto {
  return {
    usuarioId,
    ligaId,
    fechaInscripcion: new Date(),
    categoria: 'RX-Masculino',
  };
}

/**
 * Datos para test de confirmación de pago
 */
export function generateConfirmacionPago(montoPagado: number = 100) {
  return {
    fechaPago: new Date(),
    metodoPago: MetodoPago.TRANSFERENCIA,
    referenciaPago: `REF-${Date.now()}`,
    montoPagado,
    comentarios: 'Pago confirmado en test',
  };
}
