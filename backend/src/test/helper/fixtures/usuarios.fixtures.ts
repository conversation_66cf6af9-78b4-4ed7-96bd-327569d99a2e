import { Ni<PERSON><PERSON>su<PERSON>, GeneroUsuario, Rol<PERSON>su<PERSON>, EstadoUsuario } from '../../../modules/usuarios/entities/usuario.entity';

/**
 * Nota sobre enums disponibles:
 * 
 * NivelUsuario: RX, INTERMEDIO, SCALED
 * GeneroUsuario: MASCULINO, FEMENINO
 * RolUsuario: USUARIO, BOX_OWNER, ADMIN
 * EstadoUsuario: ACTIVO, INACTIVO, SUSPENDIDO
 */

/**
 * Fixtures para tests de usuarios
 * 
 * Datos reutilizables para tests relacionados con usuarios
 */
export const usuariosFixtures = {
  /**
   * Usuario predeterminado para tests
   */
  defaultUser: {
    nombre: 'Usuario Predeterminado',
    alias: 'usuario_default',
    email: '<EMAIL>',
    password: 'Password123!',
    nivel: NivelUsuario.RX,
    genero: GeneroUsuario.MASCULINO,
    rol: RolUsuario.USUARIO,
    estado: EstadoUsuario.ACTIVO
  },
  
  /**
   * Usuario con rol de administrador
   */
  adminUser: {
    nombre: 'Administrador Test',
    alias: 'admin_test',
    email: '<EMAIL>',
    password: 'AdminPass123!',
    nivel: NivelUsuario.RX,
    genero: GeneroUsuario.MASCULINO,
    rol: RolUsuario.ADMIN,
    estado: EstadoUsuario.ACTIVO
  },
  
  /**
   * Usuario con rol de box owner (entrenador)
   */
  boxOwnerUser: {
    nombre: 'Box Owner Test',
    alias: 'boxowner_test',
    email: '<EMAIL>',
    password: 'BoxOwnerPass123!',
    nivel: NivelUsuario.INTERMEDIO,
    genero: GeneroUsuario.MASCULINO,
    rol: RolUsuario.BOX_OWNER,
    estado: EstadoUsuario.ACTIVO
  },
  
  /**
   * Usuario femenino para tests
   */
  femaleUser: {
    nombre: 'Usuaria Test',
    alias: 'usuaria_test',
    email: '<EMAIL>',
    password: 'FemalePass123!',
    nivel: NivelUsuario.SCALED,
    genero: GeneroUsuario.FEMENINO,
    rol: RolUsuario.USUARIO,
    estado: EstadoUsuario.ACTIVO
  },
  
  /**
   * Usuario inactivo para tests
   */
  inactiveUser: {
    nombre: 'Usuario Inactivo',
    alias: 'usuario_inactivo',
    email: '<EMAIL>',
    password: 'InactivePass123!',
    nivel: NivelUsuario.INTERMEDIO,
    genero: GeneroUsuario.MASCULINO,
    rol: RolUsuario.USUARIO,
    estado: EstadoUsuario.INACTIVO
  }
};

/**
 * Genera datos de prueba para un usuario
 *
 * @param suffix Sufijo para hacer único el usuario
 * @returns Datos del usuario de prueba
 */
export function generateTestUser(suffix: string = '') {
  return {
    nombre: `Usuario Test ${suffix}`,
    alias: `usuario_test_${suffix}`,
    email: `usuario.test.${suffix}@example.com`,
    password: 'TestPassword123!',
    nivel: NivelUsuario.RX,
    genero: GeneroUsuario.MASCULINO,
    rol: RolUsuario.USUARIO,
    estado: EstadoUsuario.ACTIVO
  };
}
