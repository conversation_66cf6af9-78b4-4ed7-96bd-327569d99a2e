import { Liga, EstadoLiga } from '../../../modules/ligas/entities/liga.entity';
import { CreateLigaDto } from '../../../modules/ligas/dto/create-liga.dto';

/**
 * Fixtures para tests de Ligas
 * 
 * Proporciona datos de prueba consistentes para los tests de ligas
 */

export interface TestLigaData {
  nombre: string;
  descripcion: string;
  fechaInicio: Date;
  fechaFin: Date;
  duracionSemanas: number;
  estado: EstadoLiga;
  precioInscripcion: number;
  tieneDescuentoEarly: boolean;
  precioEarly?: number;
  categoriaRx: boolean;
  categoriaIntermedio: boolean;
  categoriaScaled: boolean;
  generoMasculino: boolean;
  generoFemenino: boolean;
}

/**
 * Genera datos de prueba para una liga
 */
export function generateTestLiga(suffix: string = ''): TestLigaData {
  const fechaInicio = new Date();
  fechaInicio.setDate(fechaInicio.getDate() + 30); // Liga comienza en 30 días
  
  const fechaFin = new Date(fechaInicio);
  fechaFin.setDate(fechaFin.getDate() + (8 * 7)); // Liga dura 8 semanas
  
  return {
    nombre: `Liga Test ${suffix}`,
    descripcion: `Descripción de liga de prueba ${suffix}`,
    fechaInicio,
    fechaFin,
    duracionSemanas: 8,
    estado: EstadoLiga.PREPARACION,
    precioInscripcion: 100,
    tieneDescuentoEarly: true,
    precioEarly: 80,
    categoriaRx: true,
    categoriaIntermedio: true,
    categoriaScaled: true,
    generoMasculino: true,
    generoFemenino: true,
  };
}

/**
 * Genera DTO para crear una liga
 */
export function generateCreateLigaDto(suffix: string = ''): CreateLigaDto {
  const testData = generateTestLiga(suffix);
  return {
    nombre: testData.nombre,
    descripcion: testData.descripcion,
    fechaInicio: testData.fechaInicio,
    fechaFin: testData.fechaFin,
    duracionSemanas: testData.duracionSemanas,
    estado: testData.estado,
    precioInscripcion: testData.precioInscripcion,
    tieneDescuentoEarly: testData.tieneDescuentoEarly,
    precioEarly: testData.precioEarly,
    categoriaRx: testData.categoriaRx,
    categoriaIntermedio: testData.categoriaIntermedio,
    categoriaScaled: testData.categoriaScaled,
    generoMasculino: testData.generoMasculino,
    generoFemenino: testData.generoFemenino,
  };
}

/**
 * Liga activa para tests de inscripción
 */
export function generateLigaActiva(suffix: string = ''): TestLigaData {
  const fechaInicio = new Date();
  fechaInicio.setDate(fechaInicio.getDate() - 7); // Liga comenzó hace 7 días
  
  const fechaFin = new Date(fechaInicio);
  fechaFin.setDate(fechaFin.getDate() + (8 * 7)); // Liga dura 8 semanas
  
  return {
    nombre: `Liga Activa Test ${suffix}`,
    descripcion: `Liga activa para pruebas de inscripción ${suffix}`,
    fechaInicio,
    fechaFin,
    duracionSemanas: 8,
    estado: EstadoLiga.ACTIVA,
    precioInscripcion: 120,
    tieneDescuentoEarly: false,
    categoriaRx: true,
    categoriaIntermedio: true,
    categoriaScaled: true,
    generoMasculino: true,
    generoFemenino: true,
  };
}

/**
 * Liga finalizada para tests de validación
 */
export function generateLigaFinalizada(suffix: string = ''): TestLigaData {
  const fechaInicio = new Date();
  fechaInicio.setDate(fechaInicio.getDate() - 70); // Liga comenzó hace 70 días
  
  const fechaFin = new Date(fechaInicio);
  fechaFin.setDate(fechaFin.getDate() + (8 * 7)); // Liga duró 8 semanas
  
  return {
    nombre: `Liga Finalizada Test ${suffix}`,
    descripcion: `Liga finalizada para pruebas de validación ${suffix}`,
    fechaInicio,
    fechaFin,
    duracionSemanas: 8,
    estado: EstadoLiga.FINALIZADA,
    precioInscripcion: 100,
    tieneDescuentoEarly: false,
    categoriaRx: true,
    categoriaIntermedio: true,
    categoriaScaled: true,
    generoMasculino: true,
    generoFemenino: true,
  };
}

/**
 * Liga con descuento early bird
 */
export function generateLigaConDescuentoEarly(suffix: string = ''): TestLigaData {
  const fechaInicio = new Date();
  fechaInicio.setDate(fechaInicio.getDate() + 45); // Liga comienza en 45 días (más de un mes)
  
  const fechaFin = new Date(fechaInicio);
  fechaFin.setDate(fechaFin.getDate() + (8 * 7)); // Liga dura 8 semanas
  
  return {
    nombre: `Liga Early Bird Test ${suffix}`,
    descripcion: `Liga con descuento early bird para pruebas ${suffix}`,
    fechaInicio,
    fechaFin,
    duracionSemanas: 8,
    estado: EstadoLiga.PREPARACION,
    precioInscripcion: 150,
    tieneDescuentoEarly: true,
    precioEarly: 120,
    categoriaRx: true,
    categoriaIntermedio: true,
    categoriaScaled: true,
    generoMasculino: true,
    generoFemenino: true,
  };
}
